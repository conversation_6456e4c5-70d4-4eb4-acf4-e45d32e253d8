<?xml version="1.1" encoding="UTF-8"?>
<xwikidoc version="1.5" reference="WatermarkExtension.WatermarkConfiguration" locale="">
    <web>WatermarkExtension</web>
    <name>WatermarkConfiguration</name>
    <language/>
    <defaultLanguage/>
    <translation>0</translation>
    <creator>xwiki:XWiki.Admin</creator>
    <parent>WatermarkExtension.WebHome</parent>
    <author>xwiki:XWiki.Admin</author>
    <contentAuthor>xwiki:XWiki.Admin</contentAuthor>
    <version>1.1</version>
    <title>Watermark Configuration</title>
    <comment>Main configuration page for the Watermark Extension</comment>
    <minorEdit>false</minorEdit>
    <syntaxId>xwiki/2.1</syntaxId>
    <hidden>false</hidden>

    <object>
        <name>WatermarkExtension.WatermarkConfiguration</name>
        <number>0</number>
        <className>XWiki.ConfigurableClass</className>
        <guid>a7c5a47e-a169-4235-912a-8c5e6d6349c2</guid>
        <class>
            <name>XWiki.ConfigurableClass</name>
            <customClass/>
            <customMapping/>
            <defaultViewSheet/>
            <defaultEditSheet/>
            <defaultWeb/>
            <nameField/>
            <validationScript/>
            <configurationClass>
                <cache>0</cache>
                <classType>com.xpn.xwiki.objects.classes.StringClass</classType>
                <customDisplay/>
                <disabled>0</disabled>
                <name>configurationClass</name>
                <number>1</number>
                <prettyName>Configuration class</prettyName>
                <size>30</size>
                <unmodifiable>0</unmodifiable>
                <validationMessage/>
                <validationRegExp/>
            </configurationClass>
            <displayInCategory>
                <cache>0</cache>
                <classType>com.xpn.xwiki.objects.classes.StringClass</classType>
                <customDisplay/>
                <disabled>0</disabled>
                <name>displayInCategory</name>
                <number>2</number>
                <prettyName>Display in category</prettyName>
                <size>30</size>
                <unmodifiable>0</unmodifiable>
                <validationMessage/>
                <validationRegExp/>
            </displayInCategory>
            <displayInSection>
                <cache>0</cache>
                <classType>com.xpn.xwiki.objects.classes.StringClass</classType>
                <customDisplay/>
                <disabled>0</disabled>
                <name>displayInSection</name>
                <number>3</number>
                <prettyName>Display in section</prettyName>
                <size>30</size>
                <unmodifiable>0</unmodifiable>
                <validationMessage/>
                <validationRegExp/>
            </displayInSection>
            <scope>
                <cache>0</cache>
                <classType>com.xpn.xwiki.objects.classes.StringClass</classType>
                <customDisplay/>
                <disabled>0</disabled>
                <name>scope</name>
                <number>4</number>
                <prettyName>Scope</prettyName>
                <size>30</size>
                <unmodifiable>0</unmodifiable>
                <validationMessage/>
                <validationRegExp/>
            </scope>
        </class>
        <property>
            <configurationClass>WatermarkExtension.WatermarkConfigClass</configurationClass>
        </property>
        <property>
            <displayInCategory>watermark</displayInCategory>
        </property>
        <property>
            <displayInSection>watermark.configuration</displayInSection>
        </property>
        <property>
            <scope>WIKI</scope>
        </property>
    </object>
    <object>
        <name>WatermarkExtension.WatermarkConfigClass</name>
        <number>0</number>
        <className>WatermarkExtension.WatermarkConfigClass</className>
        <guid>a7d65b4f-a279-4365-923a-8d5e7d6349d3</guid>
        <class>
            <name>WatermarkExtension.WatermarkConfigClass</name>
        </class>
        <property>
            <enabled>0</enabled>
        </property>
        <property>
            <textTemplate>${user} - ${timestamp}</textTemplate>
        </property>
        <property>
            <xSpacing>200</xSpacing>
        </property>
        <property>
            <ySpacing>100</ySpacing>
        </property>
        <property>
            <angle>-30</angle>
        </property>
        <property>
            <opacity>0.3</opacity>
        </property>
        <property>
            <fontSize>14</fontSize>
        </property>
        <property>
            <antiCopy>0</antiCopy>
        </property>
        <property>
            <applyToMobile>1</applyToMobile>
        </property>
    </object>

    <content>
        {{velocity}}
        #set($loc = $services.localization)

        {{info}}
        **$loc.render('watermark.config.documentation')**: [[WatermarkExtension.WebHome]]
        {{/info}}

        ## Initialize default values for WatermarkConfigClass if not already set
        #set($configDoc = $xwiki.getDocument('WatermarkExtension.WatermarkConfiguration'))
        #set($configObj = $configDoc.getObject('WatermarkExtension.WatermarkConfigClass'))

        ## Create config object if it doesn't exist, or check existing object for null values
        #if(!$configObj)
            ## Create new config object and set all default values
            #set($configObj = $configDoc.newObject('WatermarkExtension.WatermarkConfigClass'))
            #set($needsUpdate = true)

            ## Set all default values for new object
            #set($discard = $configObj.set('enabled', 0))
            #set($discard = $configObj.set('textTemplate', '${user} - ${timestamp}'))
            #set($discard = $configObj.set('xSpacing', 200))
            #set($discard = $configObj.set('ySpacing', 100))
            #set($discard = $configObj.set('angle', -30))
            #set($discard = $configObj.set('opacity', 0.3))
            #set($discard = $configObj.set('fontSize', 14))
            #set($discard = $configObj.set('antiCopy', 0))
            #set($discard = $configObj.set('applyToMobile', 1))
        #else
            ## Check existing object for null values and set defaults if needed
            #set($needsUpdate = false)

            #if($configObj.getProperty('enabled').value == null)
                #set($discard = $configObj.set('enabled', 0))
                #set($needsUpdate = true)
            #end

            #if($configObj.getProperty('textTemplate').value == null)
                #set($discard = $configObj.set('textTemplate', '${user} - ${timestamp}'))
                #set($needsUpdate = true)
            #end

            #if($configObj.getProperty('xSpacing').value == null)
                #set($discard = $configObj.set('xSpacing', 200))
                #set($needsUpdate = true)
            #end

            #if($configObj.getProperty('ySpacing').value == null)
                #set($discard = $configObj.set('ySpacing', 100))
                #set($needsUpdate = true)
            #end

            #if($configObj.getProperty('angle').value == null)
                #set($discard = $configObj.set('angle', -30))
                #set($needsUpdate = true)
            #end

            #if($configObj.getProperty('opacity').value == null)
                #set($discard = $configObj.set('opacity', 0.3))
                #set($needsUpdate = true)
            #end

            #if($configObj.getProperty('fontSize').value == null)
                #set($discard = $configObj.set('fontSize', 14))
                #set($needsUpdate = true)
            #end

            #if($configObj.getProperty('antiCopy').value == null)
                #set($discard = $configObj.set('antiCopy', 0))
                #set($needsUpdate = true)
            #end

            #if($configObj.getProperty('applyToMobile').value == null)
                #set($discard = $configObj.set('applyToMobile', 1))
                #set($needsUpdate = true)
            #end
        #end

        ## Save the document if we updated any values
        #if($needsUpdate)
            #set($discard = $configDoc.save('Initialized default watermark configuration values'))
        #end
        {{/velocity}}

        {{include reference="XWiki.ConfigurableClass"/}}
    </content>
</xwikidoc>