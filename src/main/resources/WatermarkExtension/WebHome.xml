<?xml version="1.1" encoding="UTF-8"?>
<xwikidoc version="1.5" reference="WatermarkExtension.WebHome" locale="">
  <web>WatermarkExtension</web>
  <name>WebHome</name>
  <language/>
  <defaultLanguage/>
  <translation>0</translation>
  <creator>xwiki:XWiki.Admin</creator>
  <parent>Main.WebHome</parent>
  <author>xwiki:XWiki.Admin</author>
  <contentAuthor>xwiki:XWiki.Admin</contentAuthor>
  <version>1.1</version>
  <title>$services.localization.render('watermark.home.title')</title>
  <comment/>
  <minorEdit>false</minorEdit>
  <syntaxId>xwiki/2.1</syntaxId>
  <hidden>false</hidden>
  <object>
    <name>WatermarkExtension.WebHome</name>
    <number>0</number>
    <className>XWiki.XWikiRights</className>
    <guid>watermark-admin-rights-allow</guid>
    <class>
      <name>XWiki.XWikiRights</name>
      <customClass/>
      <customMapping/>
      <defaultViewSheet/>
      <defaultEditSheet/>
      <defaultWeb/>
      <nameField/>
      <validationScript/>
      <allow>
        <cache>0</cache>
        <classType>com.xpn.xwiki.objects.classes.BooleanClass</classType>
        <customDisplay/>
        <defaultValue>1</defaultValue>
        <displayFormType>select</displayFormType>
        <displayType>allow</displayType>
        <name>allow</name>
        <number>4</number>
        <prettyName>Allow/Deny</prettyName>
        <unmodifiable>0</unmodifiable>
        <validationMessage/>
        <validationRegExp/>
      </allow>
      <groups>
        <cache>0</cache>
        <classType>com.xpn.xwiki.objects.classes.GroupsClass</classType>
        <customDisplay/>
        <defaultValue/>
        <name>groups</name>
        <number>1</number>
        <prettyName>Groups</prettyName>
        <size>5</size>
        <unmodifiable>0</unmodifiable>
        <validationMessage/>
        <validationRegExp/>
      </groups>
      <levels>
        <cache>0</cache>
        <classType>com.xpn.xwiki.objects.classes.LevelsClass</classType>
        <customDisplay/>
        <defaultValue/>
        <name>levels</name>
        <number>2</number>
        <prettyName>Levels</prettyName>
        <size>3</size>
        <unmodifiable>0</unmodifiable>
        <validationMessage/>
        <validationRegExp/>
      </levels>
      <users>
        <cache>0</cache>
        <classType>com.xpn.xwiki.objects.classes.UsersClass</classType>
        <customDisplay/>
        <defaultValue/>
        <name>users</name>
        <number>3</number>
        <prettyName>Users</prettyName>
        <size>5</size>
        <unmodifiable>0</unmodifiable>
        <validationMessage/>
        <validationRegExp/>
      </users>
    </class>
    <property>
      <allow>1</allow>
    </property>
    <property>
      <groups>XWiki.XWikiAdminGroup</groups>
    </property>
    <property>
      <levels>view,edit</levels>
    </property>
    <property>
      <users/>
    </property>
  </object>
  <object>
    <name>WatermarkExtension.WebHome</name>
    <number>1</number>
    <className>XWiki.XWikiRights</className>
    <property>
      <allow>0</allow>
    </property>
    <property>
      <groups>XWiki.XWikiAllGroup</groups>
    </property>
    <property>
      <levels>view</levels>
    </property>
    <property>
      <users/>
    </property>
  </object>
  <content><![CDATA[
{{velocity}}
#set($loc = $services.localization)

= $loc.render('watermark.home.title') =

#set($currentUser = $xcontext.user)
#set($isAdmin = $currentUser && (
    $xwiki.getUser($currentUser).isUserInGroup('XWiki.XWikiAdminGroup') ||
    $xwiki.hasAccessLevel('admin', $currentUser, 'XWiki.XWikiPreferences')
))

#if($isAdmin)
{{info}}
[[**$loc.render('watermark.home.quickconfig')**>>XWiki:XWikiPreferences]]
{{/info}}
#end

$loc.render('watermark.home.description')

== $loc.render('watermark.home.features') ==

* **$loc.render('watermark.home.features.canvas')**: $loc.render('watermark.home.features.canvas.desc')
* **$loc.render('watermark.home.features.placeholder')**: $loc.render('watermark.home.features.placeholder.desc')
* **$loc.render('watermark.home.features.config')**: $loc.render('watermark.home.features.config.desc')
* **$loc.render('watermark.home.features.anticopy')**: $loc.render('watermark.home.features.anticopy.desc')
* **$loc.render('watermark.home.features.mobile')**: $loc.render('watermark.home.features.mobile.desc')
* **$loc.render('watermark.home.features.i18n')**: $loc.render('watermark.home.features.i18n.desc')

== $loc.render('watermark.home.documentation') ==

=== $loc.render('watermark.home.documentation.params') ===

| $loc.render('watermark.home.documentation.params.parameter') | $loc.render('watermark.home.documentation.params.type') | $loc.render('watermark.home.documentation.params.range') | $loc.render('watermark.home.documentation.params.default') | $loc.render('watermark.home.documentation.params.description') |
|-----------|------|-------|---------|-------------|
| **$loc.render('watermark.admin.enabled')** | Boolean | - | `false` | $loc.render('watermark.home.documentation.params.enabled') |
| **$loc.render('watermark.admin.textTemplate')** | String | - | `${user} - ${timestamp}` | $loc.render('watermark.home.documentation.params.texttemplate') |
| **$loc.render('watermark.admin.xSpacing')** | Number | 50-500 | `200` | $loc.render('watermark.home.documentation.params.hspacing') |
| **$loc.render('watermark.admin.ySpacing')** | Number | 50-500 | `100` | $loc.render('watermark.home.documentation.params.vspacing') |
| **$loc.render('watermark.admin.angle')** | Number | -180 to 180 | `-30` | $loc.render('watermark.home.documentation.params.angle') |
| **$loc.render('watermark.admin.opacity')** | Number | 0.0-1.0 | `0.3` | $loc.render('watermark.home.documentation.params.opacity') |
| **$loc.render('watermark.admin.fontSize')** | Number | 8-48 | `14` | $loc.render('watermark.home.documentation.params.fontsize') |
| **$loc.render('watermark.admin.antiCopy')** | Boolean | - | `false` | $loc.render('watermark.home.documentation.params.anticopy') |
| **$loc.render('watermark.admin.applyToMobile')** | Boolean | - | `true` | $loc.render('watermark.home.documentation.params.mobile') |

=== $loc.render('watermark.home.placeholders') ===

* **`${user}`**: $loc.render('watermark.home.placeholders.user')
* **`${timestamp}`**: $loc.render('watermark.home.placeholders.timestamp')

== $loc.render('watermark.home.troubleshooting') ==

$loc.render('watermark.home.troubleshooting.desc')

1. **$loc.render('watermark.home.troubleshooting.config')**: $loc.render('watermark.home.troubleshooting.config.desc')
2. **$loc.render('watermark.home.troubleshooting.console')**: $loc.render('watermark.home.troubleshooting.console.desc')
3. **$loc.render('watermark.home.troubleshooting.network')**: $loc.render('watermark.home.troubleshooting.network.desc')
4. **$loc.render('watermark.home.troubleshooting.canvas')**: $loc.render('watermark.home.troubleshooting.canvas.desc')

== $loc.render('watermark.home.version') ==

* **$loc.render('watermark.home.version.extension')**: 1.0
* **$loc.render('watermark.home.version.compatibility')**: 17.4.3+
* **$loc.render('watermark.home.version.updated')**: $xwiki.formatDate($doc.date)
{{/velocity}}
]]>
</content>
</xwikidoc>
